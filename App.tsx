import React, { useState, useCallback, useEffect } from 'react';
import { AvatarDisplay } from './components/AvatarDisplay';
import { useTextToSpeech } from './hooks/useTextToSpeech';
import { useFaceLandmarker, FaceLandmarks } from './hooks/useFaceLandmarker';
import { VideoInput } from './components/VideoInput';
import { useVideoProcessor } from './hooks/useVideoProcessor';
import { BodyLandmarks } from './services/geminiService';
import { LoadingSpinner } from './components/Icons';

// Define the 5 required poses
export type ImagePose = 'neutral' | 'mouthOpen' | 'mouthTeeth' | 'eyesClosed' | 'eyesHalf';

export interface PoseData {
  src: string | null;
  landmarks: FaceLandmarks | null;
}

const initialPoseState: Record<ImagePose, PoseData> = {
  neutral: { src: null, landmarks: null },
  mouthOpen: { src: null, landmarks: null },
  mouthTeeth: { src: null, landmarks: null },
  eyesClosed: { src: null, landmarks: null },
  eyesHalf: { src: null, landmarks: null },
};

export default function App() {
  const [poses, setPoses] = useState<Record<ImagePose, PoseData>>(initialPoseState);
  const [bodyLandmarks, setBodyLandmarks] = useState<BodyLandmarks[]>([]);
  const [inputText, setInputText] = useState<string>('Hello! I am a digital avatar. Provide a video and I can speak any text you provide with a life-like animation.');

  const { speak, cancel, isSpeaking } = useTextToSpeech();
  const { detect, isModelReady } = useFaceLandmarker();
  const { processVideo, status: videoStatus, result: videoResult, error: videoError } = useVideoProcessor();

  const handleVideoSelected = useCallback(async (videoElement: HTMLVideoElement) => {
    await processVideo(videoElement);
  }, [processVideo]);

  useEffect(() => {
    if (videoResult) {
      console.log("Video processing successful, result received:", videoResult);
      const { poses: newPoses, bodyLandmarks: newBodyLandmarks } = videoResult;
      
      const processAndSetPoses = async () => {
        console.log("Processing received poses and landmarks...");
        const updatedPoses = { ...initialPoseState };
        let allLandmarksDetected = true;
        for (const key in newPoses) {
          const poseKey = key as ImagePose;
          const src = newPoses[poseKey];
          if (src) {
            const imageElement = new Image();
            imageElement.src = src;
            await new Promise(resolve => imageElement.onload = resolve);
            const detectedLandmarks = await detect(imageElement);
            if (detectedLandmarks) {
              console.log(`Landmarks detected for pose: ${poseKey}`);
              updatedPoses[poseKey] = { src, landmarks: detectedLandmarks };
            } else {
              console.error(`Landmark detection failed for pose: ${poseKey}`);
              allLandmarksDetected = false;
            }
          }
        }

        if (allLandmarksDetected) {
          console.log("All landmarks detected. Updating state.");
          setPoses(updatedPoses);
          setBodyLandmarks(newBodyLandmarks);
        } else {
          console.error("Could not detect landmarks for all required poses.");
          // Optionally, set an error state here to inform the user
        }
      };

      processAndSetPoses();
    }
  }, [videoResult, detect]);

  const allImagesProcessed = Object.values(poses).every(p => p.src && p.landmarks);

  return (
    <div className="min-h-screen bg-gray-900 text-white flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8">
      <div className="w-full max-w-6xl mx-auto">
        <header className="text-center mb-8">
          <h1 className="text-4xl sm:text-5xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-cyan-400">
            AI Photo Animator
          </h1>
          <p className="mt-2 text-lg text-gray-300">
            Provide a video or use your camera for a realistic animation of your face and body.
          </p>
        </header>

        <main className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="bg-gray-800/50 p-6 rounded-2xl border border-gray-700 shadow-2xl shadow-purple-500/10 flex flex-col space-y-6">
            <h2 className="text-2xl font-semibold text-gray-100 border-b border-gray-600 pb-3">Setup Your Avatar</h2>
            
            <VideoInput onVideoSelected={handleVideoSelected} />

            {videoStatus === 'processing' && (
              <div className="flex items-center justify-center gap-2 text-lg text-purple-300">
                <LoadingSpinner className="w-6 h-6" />
                <span>Processing video with AI... This may take a moment.</span>
              </div>
            )}

            {videoStatus === 'error' && (
              <div className="text-center text-red-400">
                <p><strong>Error processing video:</strong></p>
                <p>{videoError}</p>
              </div>
            )}

            {allImagesProcessed && (
              <div>
                  <label htmlFor="avatar-text" className="block text-sm font-medium text-gray-300 mb-2">
                      Enter Text to Animate
                  </label>
                  <textarea
                      id="avatar-text"
                      value={inputText}
                      onChange={(e) => setInputText(e.target.value)}
                      placeholder="Enter text here..."
                      className="w-full h-24 p-3 bg-gray-900 border border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-shadow duration-200 resize-none"
                      rows={3}
                  />
              </div>
            )}
          </div>

          <div className="bg-gray-800/50 p-6 rounded-2xl border border-gray-700 shadow-2xl shadow-cyan-500/10 flex flex-col">
            <h2 className="text-2xl font-semibold text-gray-100 border-b border-gray-600 pb-3 mb-6">Animate!</h2>
            <AvatarDisplay
              poses={poses}
              bodyLandmarks={bodyLandmarks}
              isSpeaking={isSpeaking}
              onPlayAudio={() => speak(inputText)}
              onCancelAudio={cancel}
              hasContent={allImagesProcessed && !!inputText}
            />
          </div>
        </main>
      </div>
    </div>
  );
}
