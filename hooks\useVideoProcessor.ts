import { useState, useCallback } from 'react';
import { processVideoForPoses, ProcessedVideoResult } from '../services/geminiService';

type VideoProcessingStatus = 'idle' | 'processing' | 'success' | 'error';

interface UseVideoProcessorReturn {
  processVideo: (videoElement: HTMLVideoElement) => Promise<void>;
  status: VideoProcessingStatus;
  result: ProcessedVideoResult | null;
  error: string | null;
}

export function useVideoProcessor(): UseVideoProcessorReturn {
  const [status, setStatus] = useState<VideoProcessingStatus>('idle');
  const [result, setResult] = useState<ProcessedVideoResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const processVideo = useCallback(async (videoElement: HTMLVideoElement) => {
    setStatus('processing');
    setResult(null);
    setError(null);

    try {
      const processedResult = await processVideoForPoses(videoElement);
      setResult(processedResult);
      setStatus('success');
    } catch (e: any) {
      console.error("Failed to process video:", e);
      setError(e.message || 'An unknown error occurred during video processing.');
      setStatus('error');
    }
  }, []);

  return { processVideo, status, result, error };
}
