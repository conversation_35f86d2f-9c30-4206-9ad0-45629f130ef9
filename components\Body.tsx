import React from 'react';
import { BodyLandmarks } from '../services/geminiService';
import { ImageBounds } from './AvatarDisplay';

interface BodyProps {
  bodyLandmarks: BodyLandmarks | null;
  imageSrc: string | null;
  imageBounds: ImageBounds;
}

export const Body: React.FC<BodyProps> = ({ bodyLandmarks, imageSrc, imageBounds }) => {
  if (!bodyLandmarks || !imageSrc) {
    return null;
  }

  // This is a placeholder for the body rendering logic.
  // For now, we'll just draw a simple representation of the shoulders.
  const leftShoulder = bodyLandmarks.left_shoulder;
  const rightShoulder = bodyLandmarks.right_shoulder;

  if (!leftShoulder || !rightShoulder) {
    return null;
  }

  const scaleX = imageBounds.width;
  const scaleY = imageBounds.height;

  return (
    <svg
      className="absolute top-0 left-0 w-full h-full"
      viewBox={`0 0 ${imageBounds.width} ${imageBounds.height}`}
    >
      <line
        x1={leftShoulder.x * scaleX}
        y1={leftShoulder.y * scaleY}
        x2={rightShoulder.x * scaleX}
        y2={rightShoulder.y * scaleY}
        stroke="cyan"
        strokeWidth="2"
      />
    </svg>
  );
};
