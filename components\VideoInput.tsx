import React, { useRef, useState } from 'react';
import { UploadIcon, VideoCameraIcon } from './Icons';

interface VideoInputProps {
  onVideoSelected: (videoElement: HTMLVideoElement) => void;
}

export const VideoInput: React.FC<VideoInputProps> = ({ onVideoSelected }) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const [videoSrc, setVideoSrc] = useState<string | null>(null);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const url = URL.createObjectURL(file);
      setVideoSrc(url);
    }
  };

  const handleUseCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true });
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setVideoSrc('live');
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('Could not access the camera. Please ensure you have given permission.');
    }
  };

  const handleProcessVideo = () => {
    if (videoRef.current) {
      onVideoSelected(videoRef.current);
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-center gap-4">
        <button
          onClick={() => inputRef.current?.click()}
          className="flex items-center gap-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg transition-colors"
        >
          <UploadIcon className="w-5 h-5" />
          Upload Video
        </button>
        <input type="file" accept="video/*" ref={inputRef} onChange={handleFileChange} className="hidden" />
        <button
          onClick={handleUseCamera}
          className="flex items-center gap-2 px-4 py-2 bg-cyan-600 hover:bg-cyan-700 text-white font-semibold rounded-lg transition-colors"
        >
          <VideoCameraIcon className="w-5 h-5" />
          Use Camera
        </button>
      </div>
      
      {videoSrc && (
        <div className="w-full aspect-video bg-black rounded-lg overflow-hidden">
          <video ref={videoRef} src={videoSrc === 'live' ? undefined : videoSrc} autoPlay playsInline muted className="w-full h-full object-contain" />
        </div>
      )}

      {videoSrc && (
        <button
          onClick={handleProcessVideo}
          className="w-full px-4 py-3 bg-green-600 hover:bg-green-700 text-white font-bold rounded-lg transition-colors"
        >
          Process Video
        </button>
      )}
    </div>
  );
};
