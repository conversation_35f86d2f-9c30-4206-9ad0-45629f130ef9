import { useState, useEffect, useCallback } from 'react';
import { FaceLandmarker, FilesetResolver } from '@mediapipe/tasks-vision';
import type { NormalizedLandmark } from '@mediapipe/tasks-vision';

export interface LipLandmarks {
  outerUpper: NormalizedLandmark[];
  innerUpper: NormalizedLandmark[];
  outerLower: NormalizedLandmark[];
  innerLower: NormalizedLandmark[];
}

export interface EyeLandmarks {
  left: NormalizedLandmark[];
  right: NormalizedLandmark[];
}

export interface FaceLandmarks {
  lips: LipLandmarks;
  eyes: EyeLandmarks;
}

// Based on https://developers.google.com/mediapipe/solutions/vision/face_landmarker#face_landmarks
const landmarkIndices = {
  lips: {
    outerUpper: [61, 185, 40, 39, 37, 0, 267, 269, 270, 409, 291],
    innerUpper: [78, 191, 80, 81, 82, 13, 312, 311, 310, 415, 308],
    outerLower: [61, 146, 91, 181, 84, 17, 314, 405, 321, 375, 291],
    innerLower: [78, 95, 88, 178, 87, 14, 317, 402, 318, 324, 308],
  },
  eyes: {
    // These are the contours of the eyes, used for masking.
    left: [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246],
    right: [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398],
  }
};


export const useFaceLandmarker = () => {
  const [faceLandmarker, setFaceLandmarker] = useState<FaceLandmarker | null>(null);
  const [isModelReady, setIsModelReady] = useState(false);

  useEffect(() => {
    const createFaceLandmarker = async () => {
      try {
        const vision = await FilesetResolver.forVisionTasks(
          "https://cdn.jsdelivr.net/npm/@mediapipe/tasks-vision@latest/wasm"
        );
        const newFaceLandmarker = await FaceLandmarker.createFromOptions(vision, {
          baseOptions: {
            modelAssetPath: `https://storage.googleapis.com/mediapipe-models/face_landmarker/face_landmarker/float16/1/face_landmarker.task`,
            delegate: "GPU"
          },
          outputFaceBlendshapes: false,
          outputFacialTransformationMatrixes: false,
          runningMode: 'IMAGE',
          numFaces: 1
        });
        setFaceLandmarker(newFaceLandmarker);
        setIsModelReady(true);
      } catch (e) {
        console.error("Failed to create FaceLandmarker", e);
        // This error is critical and will be handled by the component not becoming ready
      }
    };
    createFaceLandmarker();

    return () => {
      faceLandmarker?.close();
    }
  }, []);

  const detect = useCallback(async (imageElement: HTMLImageElement): Promise<FaceLandmarks | null> => {
    if (!faceLandmarker) {
      console.error("FaceLandmarker is not initialized.");
      return null;
    }

    const result = faceLandmarker.detect(imageElement);
    if (result.faceLandmarks.length === 0) {
      return null;
    }

    const landmarks = result.faceLandmarks[0];
    
    const faceLandmarks: FaceLandmarks = {
      lips: {
        outerUpper: landmarkIndices.lips.outerUpper.map(i => landmarks[i]),
        innerUpper: landmarkIndices.lips.innerUpper.map(i => landmarks[i]),
        outerLower: landmarkIndices.lips.outerLower.map(i => landmarks[i]),
        innerLower: landmarkIndices.lips.innerLower.map(i => landmarks[i]),
      },
      eyes: {
        left: landmarkIndices.eyes.left.map(i => landmarks[i]),
        right: landmarkIndices.eyes.right.map(i => landmarks[i]),
      }
    };

    return faceLandmarks;

  }, [faceLandmarker]);

  return { detect, isModelReady };
};