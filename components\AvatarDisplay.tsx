import React, { useEffect, useState, useRef } from 'react';
import { PlayIcon, StopIcon, UserCircleIcon } from './Icons';
import { Mouth } from './Mouth';
import { Eyes } from './Eyes';
import { Body } from './Body';
import type { PoseData, ImagePose } from '../App';
import { BodyLandmarks } from '../services/geminiService';

export type MouthPose = 'CLOSED' | 'OPEN' | 'TEETH';
export type EyePose = 'OPEN' | 'HALF' | 'CLOSED';

interface AvatarDisplayProps {
  poses: Record<ImagePose, PoseData>;
  bodyLandmarks: BodyLandmarks[];
  isSpeaking: boolean;
  onPlayAudio: () => void;
  onCancelAudio: () => void;
  hasContent: boolean;
}

export interface ImageBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export const AvatarDisplay: React.FC<AvatarDisplayProps> = ({
  poses,
  bodyLandmarks,
  isSpeaking,
  onPlayAudio,
  onCancelAudio,
  hasContent,
}) => {
  const [mouthPose, setMouthPose] = useState<MouthPose>('CLOSED');
  const [eyePose, setEyePose] = useState<EyePose>('OPEN');
  const [bodyPose, setBodyPose] = useState<BodyLandmarks | null>(null);
  const [imageBounds, setImageBounds] = useState<ImageBounds | null>(null);
  const imageRef = useRef<HTMLImageElement>(null);

  const avatarUrl = poses.neutral.src;
  const neutralLandmarks = poses.neutral.landmarks;

  const handlePlayButtonClick = () => {
    if (isSpeaking) {
      onCancelAudio();
    } else if (hasContent) {
      onPlayAudio();
    }
  };

  const calculateImageBounds = () => {
    const img = imageRef.current;
    if (img) {
      const { naturalWidth, naturalHeight, offsetWidth, offsetHeight, offsetLeft, offsetTop } = img;
      const naturalAspectRatio = naturalWidth / naturalHeight;
      const containerAspectRatio = offsetWidth / offsetHeight;
      let renderWidth = offsetWidth;
      let renderHeight = offsetHeight;
      let xOffset = 0;
      let yOffset = 0;
      if (naturalAspectRatio > containerAspectRatio) {
        renderHeight = offsetWidth / naturalAspectRatio;
        yOffset = (offsetHeight - renderHeight) / 2;
      } else {
        renderWidth = offsetHeight * naturalAspectRatio;
        xOffset = (offsetWidth - renderWidth) / 2;
      }
      setImageBounds({ x: offsetLeft + xOffset, y: offsetTop + yOffset, width: renderWidth, height: renderHeight });
    }
  };

  useEffect(() => {
    const img = imageRef.current;
    if (img) {
      const handleLoad = () => calculateImageBounds();
      if (img.complete) {
        handleLoad();
      } else {
        img.addEventListener('load', handleLoad);
      }
      const resizeObserver = new ResizeObserver(calculateImageBounds);
      resizeObserver.observe(img);
      return () => {
        img.removeEventListener('load', handleLoad);
        resizeObserver.disconnect();
      };
    }
  }, [avatarUrl]);

  useEffect(() => {
    if (isSpeaking) {
      let frame = 0;
      const animationCycle: MouthPose[] = ['OPEN', 'OPEN', 'TEETH', 'CLOSED'];
      const intervalId = setInterval(() => {
        setMouthPose(animationCycle[frame % animationCycle.length]);
        frame++;
      }, 150);
      return () => {
        clearInterval(intervalId);
        setMouthPose('CLOSED');
      };
    } else {
      setMouthPose('CLOSED');
    }
  }, [isSpeaking]);

  useEffect(() => {
    if (isSpeaking && bodyLandmarks.length > 0) {
      let frame = 0;
      const intervalId = setInterval(() => {
        setBodyPose(bodyLandmarks[frame % bodyLandmarks.length]);
        frame++;
      }, 200); // Match the frame capture rate
      return () => clearInterval(intervalId);
    }
  }, [isSpeaking, bodyLandmarks]);

  useEffect(() => {
    if (!hasContent) return;

    let timeoutId: number;
    const performBlink = () => {
      setEyePose('HALF');
      timeoutId = window.setTimeout(() => {
        setEyePose('CLOSED');
        timeoutId = window.setTimeout(() => {
          setEyePose('HALF');
          timeoutId = window.setTimeout(() => {
            setEyePose('OPEN');
            scheduleNextBlink();
          }, 75);
        }, 150);
      }, 75);
    };

    const scheduleNextBlink = () => {
      const delay = Math.random() * 4000 + 2000;
      timeoutId = window.setTimeout(performBlink, delay);
    };

    scheduleNextBlink();
    return () => window.clearTimeout(timeoutId);
  }, [hasContent]);

  const imagesForMouth = {
    closed: poses.neutral.src,
    open: poses.mouthOpen.src,
    teeth: poses.mouthTeeth.src,
  };

  const landmarksForMouth = {
    neutral: poses.neutral.landmarks?.lips,
    open: poses.mouthOpen.landmarks?.lips,
    teeth: poses.mouthTeeth.landmarks?.lips,
  };

  const imagesForEyes = {
    open: poses.neutral.src,
    half: poses.eyesHalf.src,
    closed: poses.eyesClosed.src,
  };

  const landmarksForEyes = {
    neutral: poses.neutral.landmarks?.eyes,
    half: poses.eyesHalf.landmarks?.eyes,
    closed: poses.eyesClosed.landmarks?.eyes,
  };

  return (
    <div className="w-full h-full flex flex-col items-center justify-center bg-gray-900 rounded-lg p-4 min-h-[300px] aspect-square">
      {!avatarUrl && (
        <div className="text-center text-gray-500">
          <UserCircleIcon className="w-24 h-24 mx-auto" />
          <p className="mt-4 text-lg">Your animated photo will appear here</p>
          <p className="text-sm text-gray-600">Please upload all five images and wait for success.</p>
        </div>
      )}

      {avatarUrl && (
        <div className="w-full h-full flex flex-col items-center justify-center gap-4">
          <div className="relative w-full aspect-square max-w-md rounded-lg overflow-hidden shadow-2xl shadow-black bg-gray-800 flex items-center justify-center">
            <img ref={imageRef} src={avatarUrl} alt="Uploaded Avatar" className="w-full h-full object-contain" key={avatarUrl} />
            
            {hasContent && imageBounds && (
              <>
                <Mouth 
                  mouthPose={mouthPose}
                  images={imagesForMouth as Record<'closed' | 'open' | 'teeth', string>}
                  landmarks={landmarksForMouth as any}
                  imageBounds={imageBounds} 
                />
                <Eyes 
                  eyePose={eyePose}
                  images={imagesForEyes as Record<'open' | 'half' | 'closed', string>}
                  landmarks={landmarksForEyes as any}
                  imageBounds={imageBounds} 
                />
                <Body
                  bodyLandmarks={bodyPose}
                  imageSrc={avatarUrl}
                  imageBounds={imageBounds}
                />
              </>
            )}

          </div>
          <button
            onClick={handlePlayButtonClick}
            disabled={!hasContent}
            className={`flex items-center justify-center gap-2 px-6 py-3 rounded-full text-lg font-semibold transition-all duration-200 ${
              !hasContent
                ? 'bg-gray-600 cursor-not-allowed text-gray-400'
                : isSpeaking
                ? 'bg-red-600 hover:bg-red-700 text-white'
                : 'bg-cyan-500 hover:bg-cyan-600 text-white'
            }`}
          >
            {isSpeaking ? (
              <>
                <StopIcon className="w-6 h-6" />
                <span>Stop</span>
              </>
            ) : (
              <>
                <PlayIcon className="w-6 h-6" />
                <span>Animate</span>
              </>
            )}
          </button>
        </div>
      )}
    </div>
  );
};
