import { useState, useCallback, useEffect } from 'react';

export const useTextToSpeech = () => {
  const [isSpeaking, setIsSpeaking] = useState(false);

  const speak = useCallback((text: string) => {
    if (!window.speechSynthesis) {
      console.error("<PERSON>rowser does not support text-to-speech.");
      return;
    }
    
    // Cancel any ongoing speech before starting a new one
    window.speechSynthesis.cancel();

    const newUtterance = new SpeechSynthesisUtterance(text);
    newUtterance.onstart = () => {
      setIsSpeaking(true);
    };
    newUtterance.onend = () => setIsSpeaking(false);
    newUtterance.onerror = (event) => {
        // CRITICAL FIX: The 'interrupted' error is expected when we call cancel().
        // We should not log it as an error to the console.
        if (event.error !== 'interrupted') {
            console.error("Speech synthesis error:", event.error);
        }
        setIsSpeaking(false);
    };
    
    window.speechSynthesis.speak(newUtterance);
  }, []);

  const cancel = useCallback(() => {
    if (window.speechSynthesis) {
      window.speechSynthesis.cancel();
      // The onend/onerror handlers of the utterance will set isSpeaking to false.
    }
  }, []);
  
  // Cleanup on unmount
  useEffect(() => {
      return () => {
          if (window.speechSynthesis && window.speechSynthesis.speaking) {
              window.speechSynthesis.cancel();
          }
      };
  }, []);

  return { speak, cancel, isSpeaking };
};