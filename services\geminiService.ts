import { GoogleGenerativeAI } from "@google/generative-ai";
import { ImagePose } from "../App";

const API_KEY = "AIzaSyB8akeMtOrWHqb6sBHfP1h_3SEtSzzWHtw";

const genAI = new GoogleGenerativeAI(API_KEY);

// Define a type for the body landmarks for clarity
export interface BodyLandmarks {
  // Define the structure of body landmarks based on what the model will provide
  // This is a placeholder and will need to be updated.
  [key: string]: { x: number; y: number; z: number };
}

export interface ProcessedVideoResult {
  poses: Record<ImagePose, string>; // base64 encoded images
  bodyLandmarks: BodyLandmarks[]; // A series of body landmark data
}

export async function processVideoForPoses(
  videoElement: HTMLVideoElement
): Promise<ProcessedVideoResult> {
  console.log("Processing video with Gemini...");

  // 1. Set up the model
  const model = genAI.getGenerativeModel({ model: "gemini-1.5-pro-latest" });

  // 2. Capture frames from the video
  const canvas = document.createElement('canvas');
  const context = canvas.getContext('2d');
  if (!context) throw new Error("Canvas context not available");

  const frames: string[] = [];
  const captureInterval = 200; // ms, capture 5 frames per second
  const duration = videoElement.duration;
  let currentTime = 0;

  videoElement.pause();
  videoElement.currentTime = 0;

  return new Promise(async (resolve, reject) => {
    const captureFrame = async () => {
      if (currentTime >= duration) {
        // Once all frames are captured, send them to the model
        try {
          const result = await sendFramesToGemini(model, frames);
          resolve(result);
        } catch (error) {
          reject(error);
        }
        return;
      }

      videoElement.currentTime = currentTime;
      // Wait for the video to seek to the right time
      videoElement.onseeked = () => {
        canvas.width = videoElement.videoWidth;
        canvas.height = videoElement.videoHeight;
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        const base64Frame = canvas.toDataURL('image/jpeg');
        frames.push(base64Frame);
        currentTime += captureInterval / 1000;
        captureFrame();
      };
    };

    await captureFrame();
  });
}

async function sendFramesToGemini(model: any, frames: string[]): Promise<ProcessedVideoResult> {
  const prompt = `
    Analyze the following sequence of video frames. Your task is to select the 5 best frames that correspond to the following facial expressions:
    1.  'neutral': A clear, forward-facing shot with the mouth closed and eyes open.
    2.  'mouthOpen': A clear shot with the mouth open, as if speaking.
    3.  'mouthTeeth': A clear shot with the teeth visible.
    4.  'eyesClosed': A clear shot with the eyes fully closed.
    5.  'eyesHalf': A clear shot with the eyes half-closed, as in a blink.

    Additionally, for each frame, analyze the body posture and provide a set of body landmarks.

    For each of the 5 required poses, return the index of the best frame from the provided array.
    Also, return the body landmarks for all frames.

    The output should be a JSON object with the following structure:
    {
      "selectedFrames": {
        "neutral": <frame_index>,
        "mouthOpen": <frame_index>,
        "mouthTeeth": <frame_index>,
        "eyesClosed": <frame_index>,
        "eyesHalf": <frame_index>
      },
      "bodyLandmarks": [
        { "left_shoulder": { "x": ..., "y": ..., "z": ... }, "right_shoulder": { ... }, ... },
        ...
      ]
    }
  `;

  const fileUploadParts = frames.map((frame, index) => ({
    inlineData: {
      data: frame.split(',')[1],
      mimeType: 'image/jpeg'
    }
  }));

  const result = await model.generateContent([prompt, ...fileUploadParts]);
  const response = await result.response;
  const text = response.text();
  
  console.log("Gemini response:", text);

  // This is a placeholder for parsing the response and extracting the data
  const parsedResult = JSON.parse(text);

  const poses: Record<ImagePose, string> = {
    neutral: frames[parsedResult.selectedFrames.neutral],
    mouthOpen: frames[parsedResult.selectedFrames.mouthOpen],
    mouthTeeth: frames[parsedResult.selectedFrames.mouthTeeth],
    eyesClosed: frames[parsedResult.selectedFrames.eyesClosed],
    eyesHalf: frames[parsedResult.selectedFrames.eyesHalf],
  };

  return {
    poses,
    bodyLandmarks: parsedResult.bodyLandmarks,
  };
}
