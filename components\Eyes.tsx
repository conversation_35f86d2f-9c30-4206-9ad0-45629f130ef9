import React, { useMemo } from 'react';
import type { EyeLandmarks } from '../hooks/useFaceLandmarker';
import type { NormalizedLandmark } from '@mediapipe/tasks-vision';
import type { ImageBounds, EyePose } from './AvatarDisplay';

interface EyeLandmarksMap {
  neutral: EyeLandmarks;
  half: EyeLandmarks;
  closed: EyeLandmarks;
}

export interface EyeImages {
  open: string;
  half: string;
  closed: string;
}

interface EyeLayerProps {
  isVisible: boolean;
  neutralLandmarks: NormalizedLandmark[];
  poseLandmarks: NormalizedLandmark[];
  imageUrl: string;
  imageBounds: ImageBounds;
}

const getCenterPoint = (landmarks: NormalizedLandmark[]): { x: number; y: number } => {
  if (!landmarks || landmarks.length === 0) return { x: 0, y: 0 };
  const { x, y } = landmarks.reduce((acc, p) => ({ x: acc.x + p.x, y: acc.y + p.y }), { x: 0, y: 0 });
  return { x: x / landmarks.length, y: y / landmarks.length };
};

const toPolygon = (points: NormalizedLandmark[]): string => {
  if (!points || points.length === 0) return 'none';
  return `polygon(${points.map(p => `${p.x * 100}% ${p.y * 100}%`).join(', ')})`;
};

const SingleEyeLayer: React.FC<EyeLayerProps> = ({ isVisible, neutralLandmarks, poseLandmarks, imageUrl, imageBounds }) => {
  const style: React.CSSProperties = useMemo(() => {
    const neutralCenter = getCenterPoint(neutralLandmarks);
    const poseCenter = getCenterPoint(poseLandmarks);
    const offsetX = (neutralCenter.x - poseCenter.x) * imageBounds.width;
    const offsetY = (neutralCenter.y - poseCenter.y) * imageBounds.height;
    
    return {
      position: 'absolute',
      top: `${imageBounds.y}px`,
      left: `${imageBounds.x}px`,
      width: `${imageBounds.width}px`,
      height: `${imageBounds.height}px`,
      backgroundImage: `url(${imageUrl})`,
      backgroundSize: `${imageBounds.width}px ${imageBounds.height}px`,
      backgroundPosition: `${offsetX}px ${offsetY}px`,
      backgroundRepeat: 'no-repeat',
      clipPath: toPolygon(neutralLandmarks),
      opacity: isVisible ? 1 : 0,
      transition: 'opacity 75ms ease-in-out',
      zIndex: 12,
      pointerEvents: 'none',
    };
  }, [isVisible, neutralLandmarks, poseLandmarks, imageUrl, imageBounds]);

  return <div style={style} />;
};


export const Eyes: React.FC<{
  eyePose: EyePose;
  images: EyeImages;
  landmarks: EyeLandmarksMap;
  imageBounds: ImageBounds;
}> = ({ eyePose, images, landmarks, imageBounds }) => {
  if (!landmarks.neutral || !landmarks.half || !landmarks.closed || !imageBounds) return null;

  return (
    <>
      {/* Left Eye Layers */}
      <SingleEyeLayer isVisible={eyePose === 'HALF'} neutralLandmarks={landmarks.neutral.left} poseLandmarks={landmarks.half.left} imageUrl={images.half} imageBounds={imageBounds} />
      <SingleEyeLayer isVisible={eyePose === 'CLOSED'} neutralLandmarks={landmarks.neutral.left} poseLandmarks={landmarks.closed.left} imageUrl={images.closed} imageBounds={imageBounds} />
      
      {/* Right Eye Layers */}
      <SingleEyeLayer isVisible={eyePose === 'HALF'} neutralLandmarks={landmarks.neutral.right} poseLandmarks={landmarks.half.right} imageUrl={images.half} imageBounds={imageBounds} />
      <SingleEyeLayer isVisible={eyePose === 'CLOSED'} neutralLandmarks={landmarks.neutral.right} poseLandmarks={landmarks.closed.right} imageUrl={images.closed} imageBounds={imageBounds} />
    </>
  );
};