import React, { useMemo } from 'react';
import type { LipLandmarks } from '../hooks/useFaceLandmarker';
import type { NormalizedLandmark } from '@mediapipe/tasks-vision';
import type { ImageBounds, MouthPose } from './AvatarDisplay';

interface MouthLandmarksMap {
  neutral: LipLandmarks;
  open: LipLandmarks;
  teeth: LipLandmarks;
}

interface MouthImages {
    closed: string;
    open: string;
    teeth: string;
}

interface MouthProps {
  mouthPose: MouthPose;
  images: MouthImages;
  landmarks: MouthLandmarksMap;
  imageBounds: ImageBounds;
}

const getCenterPoint = (landmarks: NormalizedLandmark[]): { x: number, y: number } => {
    const { x, y } = landmarks.reduce((acc, p) => ({ x: acc.x + p.x, y: acc.y + p.y }), { x: 0, y: 0 });
    return { x: x / landmarks.length, y: y / landmarks.length };
};

const toPolygon = (points: NormalizedLandmark[]): string => {
    if (!points || points.length === 0) return 'none';
    const allPoints = [...points, points[0]];
    return `polygon(${allPoints.map(p => `${p.x * 100}% ${p.y * 100}%`).join(', ')})`;
};

export const Mouth: React.FC<MouthProps> = ({ mouthPose, images, landmarks, imageBounds }) => {
  const { openStyle, teethStyle } = useMemo(() => {
    if (!landmarks.neutral || !landmarks.open || !landmarks.teeth || !imageBounds) {
      return { openStyle: {}, teethStyle: {} };
    }
    
    const mouthPolygon = toPolygon([...landmarks.neutral.outerUpper, ...landmarks.neutral.outerLower.reverse()]);

    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      top: `${imageBounds.y}px`,
      left: `${imageBounds.x}px`,
      width: `${imageBounds.width}px`,
      height: `${imageBounds.height}px`,
      clipPath: mouthPolygon,
      backgroundSize: `${imageBounds.width}px ${imageBounds.height}px`,
      backgroundRepeat: 'no-repeat',
      transition: 'opacity 50ms ease-in-out',
      pointerEvents: 'none',
      zIndex: 10,
    };
    
    // Calculate alignment for 'open' pose
    const neutralMouthCenter = getCenterPoint([...landmarks.neutral.outerUpper, ...landmarks.neutral.outerLower]);
    const openMouthCenter = getCenterPoint([...landmarks.open.outerUpper, ...landmarks.open.outerLower]);
    const openOffsetX = (neutralMouthCenter.x - openMouthCenter.x) * imageBounds.width;
    const openOffsetY = (neutralMouthCenter.y - openMouthCenter.y) * imageBounds.height;
    
    const openStyle: React.CSSProperties = {
      ...baseStyle,
      backgroundImage: `url(${images.open})`,
      backgroundPosition: `${openOffsetX}px ${openOffsetY}px`,
      opacity: mouthPose === 'OPEN' ? 1 : 0,
    };

    // Calculate alignment for 'teeth' pose
    const teethMouthCenter = getCenterPoint([...landmarks.teeth.outerUpper, ...landmarks.teeth.outerLower]);
    const teethOffsetX = (neutralMouthCenter.x - teethMouthCenter.x) * imageBounds.width;
    const teethOffsetY = (neutralMouthCenter.y - teethMouthCenter.y) * imageBounds.height;

    const teethStyle: React.CSSProperties = {
      ...baseStyle,
      backgroundImage: `url(${images.teeth})`,
      backgroundPosition: `${teethOffsetX}px ${teethOffsetY}px`,
      opacity: mouthPose === 'TEETH' ? 1 : 0,
    };
    
    return { openStyle, teethStyle };
  }, [landmarks, images, mouthPose, imageBounds]);

  if (!landmarks.neutral) return null;

  return (
    <>
      <div style={openStyle} />
      <div style={teethStyle} />
    </>
  );
};